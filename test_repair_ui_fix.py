#!/usr/bin/env python3
"""
Test script to verify that repair_ui.py works without AttributeError
"""

import pygame
import sys
import os

def test_repair_ui_import():
    """Test that repair_ui can be imported without errors"""
    print("=== Testing repair_ui Import ===")
    
    try:
        import repair_ui
        print("✅ repair_ui imported successfully")
        return True
    except Exception as e:
        print(f"❌ Error importing repair_ui: {e}")
        return False

def test_repair_ui_globals():
    """Test that global variables are properly defined"""
    print("\n=== Testing repair_ui Global Variables ===")
    
    try:
        import repair_ui
        
        # Check if global variables exist
        if hasattr(repair_ui, '_maintenance_last_click_time'):
            print("✅ _maintenance_last_click_time global variable exists")
        else:
            print("❌ _maintenance_last_click_time global variable missing")
            return False
            
        if hasattr(repair_ui, '_repair_last_click_times'):
            print("✅ _repair_last_click_times global variable exists")
        else:
            print("❌ _repair_last_click_times global variable missing")
            return False
            
        # Check initial values
        if repair_ui._maintenance_last_click_time == 0:
            print("✅ _maintenance_last_click_time initialized correctly")
        else:
            print("❌ _maintenance_last_click_time not initialized to 0")
            return False
            
        if repair_ui._repair_last_click_times == {}:
            print("✅ _repair_last_click_times initialized correctly")
        else:
            print("❌ _repair_last_click_times not initialized to empty dict")
            return False
            
        return True
    except Exception as e:
        print(f"❌ Error testing global variables: {e}")
        return False

def test_repair_ui_function_exists():
    """Test that draw_repair_screen function exists"""
    print("\n=== Testing repair_ui Function ===")
    
    try:
        import repair_ui
        
        if hasattr(repair_ui, 'draw_repair_screen'):
            print("✅ draw_repair_screen function exists")
            
            # Check if it's callable
            if callable(repair_ui.draw_repair_screen):
                print("✅ draw_repair_screen is callable")
                return True
            else:
                print("❌ draw_repair_screen is not callable")
                return False
        else:
            print("❌ draw_repair_screen function missing")
            return False
            
    except Exception as e:
        print(f"❌ Error testing function: {e}")
        return False

def main():
    """Run all tests"""
    print("🔧 Repair UI Fix - Test Suite")
    print("=" * 50)
    
    tests = [
        test_repair_ui_import,
        test_repair_ui_globals,
        test_repair_ui_function_exists
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
        except Exception as e:
            print(f"❌ Test failed with exception: {e}")
    
    print("\n" + "=" * 50)
    print(f"🔧 Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 Repair UI fix is working correctly!")
        print("The workshop should now work without AttributeError.")
        return True
    else:
        print("⚠️  Some issues remain - check the output above")
        return False

if __name__ == "__main__":
    main()
